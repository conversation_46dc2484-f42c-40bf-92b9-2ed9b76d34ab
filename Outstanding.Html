<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Outstanding Aging Management System</title>
    <link rel="stylesheet" href="Oustanding.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-chart-line"></i>
                    <h1>Outstanding Aging System</h1>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <button class="btn btn-secondary" id="exportBtn">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Cards -->
            <section class="dashboard-cards">
                <div class="card summary-card">
                    <div class="card-icon total">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="card-content">
                        <h3>Total Outstanding</h3>
                        <p class="amount" id="totalOutstanding">$0.00</p>
                        <span class="trend">All receivables</span>
                    </div>
                </div>

                <div class="card summary-card">
                    <div class="card-icon overdue">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="card-content">
                        <h3>Overdue Amount</h3>
                        <p class="amount overdue-amount" id="overdueAmount">$0.00</p>
                        <span class="trend">Past due date</span>
                    </div>
                </div>

                <div class="card summary-card">
                    <div class="card-icon current">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="card-content">
                        <h3>Current (0-30 days)</h3>
                        <p class="amount" id="currentAmount">$0.00</p>
                        <span class="trend">Within terms</span>
                    </div>
                </div>

                <div class="card summary-card">
                    <div class="card-icon credit">
                        <i class="fas fa-piggy-bank"></i>
                    </div>
                    <div class="card-content">
                        <h3>Credit Balance</h3>
                        <p class="amount credit-amount" id="creditBalance">$0.00</p>
                        <span class="trend">Advance payments</span>
                    </div>
                </div>
            </section>

            <!-- Transaction Management -->
            <section class="transaction-section">
                <div class="section-header">
                    <h2>Transaction Management</h2>
                    <div class="transaction-buttons">
                        <button class="btn btn-success" id="addInvoiceBtn">
                            <i class="fas fa-file-invoice"></i>
                            Add Invoice
                        </button>
                        <button class="btn btn-info" id="addPaymentBtn">
                            <i class="fas fa-money-bill-wave"></i>
                            Record Payment
                        </button>
                        <button class="btn btn-warning" id="addCreditNoteBtn">
                            <i class="fas fa-file-invoice-dollar"></i>
                            Credit Note
                        </button>
                        <button class="btn btn-primary" id="addAdvanceBtn">
                            <i class="fas fa-hand-holding-usd"></i>
                            Advance Payment
                        </button>
                    </div>
                </div>
            </section>

            <!-- Filters and Search -->
            <section class="filters-section">
                <div class="filters-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search by customer name, invoice number...">
                    </div>

                    <div class="filter-group">
                        <select id="customerFilter" class="filter-select">
                            <option value="">All Customers</option>
                        </select>

                        <select id="statusFilter" class="filter-select">
                            <option value="">All Status</option>
                            <option value="current">Current (0-30)</option>
                            <option value="30-60">30-60 Days</option>
                            <option value="60-90">60-90 Days</option>
                            <option value="90+">90+ Days</option>
                            <option value="credit">Credit Balance</option>
                        </select>

                        <button class="btn btn-outline" id="clearFiltersBtn">
                            <i class="fas fa-times"></i>
                            Clear Filters
                        </button>
                    </div>
                </div>
            </section>

            <!-- Outstanding Aging Table -->
            <section class="table-section">
                <div class="table-header">
                    <h2>Outstanding Aging Report</h2>
                    <div class="table-actions">
                        <button class="btn btn-outline" id="toggleViewBtn">
                            <i class="fas fa-th-list"></i>
                            Toggle View
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="aging-table" id="agingTable">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Invoice #</th>
                                <th>Date</th>
                                <th>Due Date</th>
                                <th>Total Amount</th>
                                <th>Outstanding</th>
                                <th>Current</th>
                                <th>30-60 Days</th>
                                <th>60-90 Days</th>
                                <th>90+ Days</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="agingTableBody">
                            <!-- Dynamic content will be inserted here -->
                        </tbody>
                    </table>
                </div>

                <!-- Empty State -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <i class="fas fa-inbox"></i>
                    <h3>No Outstanding Records</h3>
                    <p>Start by adding invoices or recording transactions</p>
                    <button class="btn btn-primary" id="addFirstInvoiceBtn">
                        <i class="fas fa-plus"></i>
                        Add First Invoice
                    </button>
                </div>
            </section>
        </main>

        <!-- Modal for Transaction Forms -->
        <div class="modal" id="transactionModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">Add Transaction</h3>
                    <button class="modal-close" id="modalCloseBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form class="transaction-form" id="transactionForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="transactionType">Transaction Type</label>
                            <select id="transactionType" name="type" required>
                                <option value="">Select Type</option>
                                <option value="invoice">Invoice</option>
                                <option value="payment">Payment</option>
                                <option value="credit_note">Credit Note</option>
                                <option value="advance">Advance Payment</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="customerName">Customer Name</label>
                            <input type="text" id="customerName" name="customer" required>
                        </div>

                        <div class="form-group">
                            <label for="documentNumber">Document Number</label>
                            <input type="text" id="documentNumber" name="documentNumber" required>
                        </div>

                        <div class="form-group">
                            <label for="amount">Amount</label>
                            <input type="number" id="amount" name="amount" step="0.01" required>
                        </div>

                        <div class="form-group">
                            <label for="transactionDate">Date</label>
                            <input type="date" id="transactionDate" name="date" required>
                        </div>

                        <div class="form-group" id="dueDateGroup">
                            <label for="dueDate">Due Date</label>
                            <input type="date" id="dueDate" name="dueDate">
                        </div>

                        <div class="form-group full-width" id="referenceGroup">
                            <label for="reference">Reference/Notes</label>
                            <textarea id="reference" name="reference" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" id="cancelBtn">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Save Transaction
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Processing...</p>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <script src="Outstanding.js"></script>
</body>
</html>