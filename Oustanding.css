/* CSS Root Variables */
:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;

    /* Secondary Colors */
    --secondary-color: #64748b;
    --secondary-hover: #475569;

    /* Status Colors */
    --success-color: #059669;
    --success-light: #d1fae5;
    --warning-color: #d97706;
    --warning-light: #fef3c7;
    --danger-color: #dc2626;
    --danger-light: #fee2e2;
    --info-color: #0891b2;
    --info-light: #cffafe;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;

    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    overflow-x: hidden;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: var(--white);
    padding: var(--spacing-4) 0;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.logo-section i {
    font-size: var(--font-size-2xl);
}

.logo-section h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: var(--spacing-3);
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-6);
    width: 100%;
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.summary-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--white);
}

.card-icon.total {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
}

.card-icon.overdue {
    background: linear-gradient(135deg, var(--danger-color), #b91c1c);
}

.card-icon.current {
    background: linear-gradient(135deg, var(--success-color), #047857);
}

.card-icon.credit {
    background: linear-gradient(135deg, var(--info-color), #0e7490);
}

.card-content h3 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-600);
    margin-bottom: var(--spacing-1);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.card-content .amount {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.card-content .amount.overdue-amount {
    color: var(--danger-color);
}

.card-content .amount.credit-amount {
    color: var(--success-color);
}

.card-content .trend {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--secondary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover:not(:disabled) {
    background: #047857;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-warning:hover:not(:disabled) {
    background: #b45309;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-info {
    background: var(--info-color);
    color: var(--white);
}

.btn-info:hover:not(:disabled) {
    background: #0e7490;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: var(--white);
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
}

.btn-outline:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Section Styles */
.transaction-section,
.filters-section,
.table-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
}

.section-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
}

.transaction-buttons {
    display: flex;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

/* Filters Section */
.filters-container {
    display: flex;
    gap: var(--spacing-4);
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    left: var(--spacing-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.search-box input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) var(--spacing-10);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.filter-group {
    display: flex;
    gap: var(--spacing-3);
    align-items: center;
}

.filter-select {
    padding: var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    background: var(--white);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

/* Table Styles */
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
}

.table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.aging-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
}

.aging-table th {
    background: var(--gray-50);
    padding: var(--spacing-4);
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 2px solid var(--gray-200);
    white-space: nowrap;
    font-size: var(--font-size-sm);
}

.aging-table td {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
    font-size: var(--font-size-sm);
}

.aging-table tbody tr {
    transition: background-color var(--transition-fast);
}

.aging-table tbody tr:hover {
    background: var(--gray-50);
}

.aging-table tbody tr:last-child td {
    border-bottom: none;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.current {
    background: var(--success-light);
    color: var(--success-color);
}

.status-badge.overdue {
    background: var(--danger-light);
    color: var(--danger-color);
}

.status-badge.credit {
    background: var(--info-light);
    color: var(--info-color);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--spacing-2);
}

.action-btn {
    padding: var(--spacing-1) var(--spacing-2);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.action-btn.edit {
    background: var(--warning-light);
    color: var(--warning-color);
}

.action-btn.delete {
    background: var(--danger-light);
    color: var(--danger-color);
}

.action-btn:hover {
    transform: scale(1.05);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-12) var(--spacing-6);
    color: var(--gray-500);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-4);
    color: var(--gray-300);
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2);
    color: var(--gray-600);
}

.empty-state p {
    margin-bottom: var(--spacing-6);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}